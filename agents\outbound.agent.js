// Handles outbound agent processing (no cron, different logic)
const fs = require('fs');
const path = require('path');
const csv = require('fast-csv');
const logger = require('../config/logger');
const PerformanceMonitor = require('../utils/performance');
const { uploadCSVFile } = require('../services/csv.service');

/**
 * Loads mapping configuration from the mappings directory
 * @param {string} mappingName - Name of the mapping file (without .mapping.json extension)
 * @returns {Object} Mapping configuration object
 */
function loadMapping(mappingName) {
    try {
        const mappingPath = path.join(__dirname, '..', 'mappings', `${mappingName}.mapping.json`);
        if (!fs.existsSync(mappingPath)) {
            throw new Error(`Mapping file not found: ${mappingPath}`);
        }
        const mappingContent = fs.readFileSync(mappingPath, 'utf8');
        return JSON.parse(mappingContent);
    } catch (error) {
        logger.error(`Error loading mapping ${mappingName}:`, error.message);
        throw error;
    }
}

/**
 * Extracts unique model names from mapping keys
 * @param {Object} mappingConfig - Mapping configuration object
 * @returns {Array<string>} Array of unique model names
 */
function extractModelNames(mappingConfig) {
    const modelNames = new Set();

    Object.keys(mappingConfig).forEach(key => {
        if (key.includes('.')) {
            const modelName = key.split('.')[0];
            modelNames.add(modelName);
        }
    });

    return Array.from(modelNames);
}

/**
 * Determines if mapping values are column names (strings) or numerical positions (numbers)
 * @param {Object} mappingConfig - Mapping configuration object
 * @returns {Object} Object with type ('column' or 'position') and processed mapping
 */
function analyzeMappingType(mappingConfig) {
    const values = Object.values(mappingConfig);

    // Check if all values are numbers (as strings or actual numbers)
    const allNumbers = values.every(value => {
        const num = Number(value);
        return !isNaN(num) && isFinite(num);
    });

    if (allNumbers) {
        // Convert string numbers to actual numbers and sort by position
        const sortedEntries = Object.entries(mappingConfig)
            .map(([key, value]) => [key, Number(value)])
            .sort((a, b) => a[1] - b[1]);

        return {
            type: 'position',
            mapping: Object.fromEntries(sortedEntries)
        };
    } else {
        return {
            type: 'column',
            mapping: mappingConfig
        };
    }
}

/**
 * Generic function to generate CSV data from any model using mapping configuration
 * @param {Object} agent - Agent configuration
 * @param {Object} performanceMonitor - Performance monitoring instance
 * @returns {Promise<string>} Path to generated CSV file
 */
async function generateCSVFromModel(agent, performanceMonitor) {
    try {
        // Load mapping configuration
        const mappingConfig = loadMapping(agent.mapping);
        logger.info(`Loaded mapping configuration for: ${agent.mapping}`);

        // Analyze mapping type (column names vs positions)
        const { type: mappingType, mapping: processedMapping } = analyzeMappingType(mappingConfig);
        logger.info(`Mapping type detected: ${mappingType}`);

        // Extract unique model names from mapping
        const modelNames = extractModelNames(mappingConfig);
        logger.info(`Models required: ${modelNames.join(', ')}`);

        // Dynamically require models
        const models = {};
        const allModels = require('../models');

        modelNames.forEach(modelName => {
            if (allModels[modelName]) {
                models[modelName] = allModels[modelName];
            } else {
                throw new Error(`Model ${modelName} not found in models`);
            }
        });

        // For now, we'll use the first model as primary (can be enhanced for joins later)
        const primaryModelName = modelNames[0];
        const primaryModel = models[primaryModelName];

        // Get all records from primary model
        performanceMonitor?.startStep(`Fetch ${primaryModelName} Data`, { agentName: agent.name });
        const records = await primaryModel.findAll({
            raw: true,
            limit: agent.batch_size || 1000 // Limit records based on batch size
        });
        performanceMonitor?.endStep(`Fetch ${primaryModelName} Data`, { recordCount: records.length });

        if (records.length === 0) {
            logger.warn(`No ${primaryModelName} records found for CSV generation`);
            return null;
        }

        // Prepare CSV data based on mapping type
        performanceMonitor?.startStep('Transform Data', { recordCount: records.length });
        const csvData = [];

        if (mappingType === 'column') {
            // Column name mapping: use values as headers
            const headers = Object.values(processedMapping);
            csvData.push(headers);

            // Transform each record
            records.forEach(record => {
                const csvRow = [];
                Object.entries(processedMapping).forEach(([modelField, csvColumn]) => {
                    // Extract field name from "Model.field_name" format
                    const fieldName = modelField.includes('.') ? modelField.split('.')[1] : modelField;
                    const value = record[fieldName] || '';
                    csvRow.push(value);
                });
                csvData.push(csvRow);
            });
        } else {
            // Position mapping: no headers, order by position
            records.forEach(record => {
                const csvRow = [];
                Object.entries(processedMapping).forEach(([modelField, position]) => {
                    // Extract field name from "Model.field_name" format
                    const fieldName = modelField.includes('.') ? modelField.split('.')[1] : modelField;
                    const value = record[fieldName] || '';
                    csvRow[position] = value; // Place value at specific position
                });
                // Fill any gaps with empty strings
                for (let i = 0; i < csvRow.length; i++) {
                    if (csvRow[i] === undefined) csvRow[i] = '';
                }
                csvData.push(csvRow);
            });
        }

        performanceMonitor?.endStep('Transform Data', { transformedRows: csvData.length - (mappingType === 'column' ? 1 : 0) });

        // Generate filename with timestamp
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `${agent.mapping}_export_${timestamp}.csv`;

        // Create temporary file for processing
        const tempDir = './downloads/temp';
        if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
        }
        const tempFilePath = path.join(tempDir, filename);

        // Write CSV file to temporary location
        performanceMonitor?.startStep('Generate CSV File', { tempFilePath, rowCount: csvData.length });
        await new Promise((resolve, reject) => {
            const writeStream = fs.createWriteStream(tempFilePath);
            csv.write(csvData, { headers: false })
                .pipe(writeStream)
                .on('finish', resolve)
                .on('error', reject);
        });
        performanceMonitor?.endStep('Generate CSV File', { fileSize: fs.statSync(tempFilePath).size });

        const dataRows = mappingType === 'column' ? csvData.length - 1 : csvData.length;
        logger.info(`CSV file generated successfully: ${tempFilePath}`);
        logger.info(`Generated ${dataRows} data rows from ${primaryModelName} model`);

        // Upload CSV file to configured destination
        performanceMonitor?.startStep('Upload CSV to Destination', {
            destination: agent.source,
            agentName: agent.name,
            fileName: filename
        });

        const uploadResult = await uploadCSVFile(tempFilePath, agent, performanceMonitor);

        performanceMonitor?.endStep('Upload CSV to Destination', {
            success: uploadResult.success,
            destination: uploadResult.destination,
            uploadedPath: uploadResult.uploadedPath,
            error: uploadResult.error
        });

        // Clean up temporary file
        try {
            fs.unlinkSync(tempFilePath);
            logger.info(`Cleaned up temporary file: ${tempFilePath}`);
        } catch (error) {
            logger.warn(`Failed to clean up temporary file ${tempFilePath}: ${error.message}`);
        }

        if (!uploadResult.success) {
            throw new Error(`Failed to upload CSV to ${agent.source}: ${uploadResult.error}`);
        }

        return {
            tempFilePath,
            uploadResult,
            dataRows,
            primaryModel: primaryModelName
        };

    } catch (error) {
        logger.error('Error generating CSV from model data:', error.message);
        throw error;
    }
}

async function outboundAgentHandler(agent) {
    const performanceMonitor = new PerformanceMonitor('Outbound CSV Generation');

    try {
        logger.info(`Processing outbound agent: ${agent.name} (Queue: ${agent.queue})`);

        performanceMonitor.startStep('Overall Outbound Processing', {
            agentName: agent.name,
            agentType: agent.type,
            mapping: agent.mapping,
            batchSize: agent.batch_size
        });

        // Generate and upload CSV file from model data
        const result = await generateCSVFromModel(agent, performanceMonitor);

        if (result) {
            const finalMetrics = performanceMonitor.complete({
                status: 'success',
                uploadedTo: result.uploadResult.destination,
                uploadedPath: result.uploadResult.uploadedPath,
                dataRows: result.dataRows,
                primaryModel: result.primaryModel,
                agentName: agent.name
            });

            logger.info('Outbound agent processing completed successfully.', {
                performanceMetrics: finalMetrics.summary,
                destination: result.uploadResult.destination,
                uploadedPath: result.uploadResult.uploadedPath,
                dataRows: result.dataRows,
                primaryModel: result.primaryModel
            });
        } else {
            performanceMonitor.complete({
                status: 'completed_no_data',
                reason: 'No records found for export'
            });
            logger.info('Outbound agent processing completed - no data to export');
        }

    } catch (error) {
        logger.error(`Error in outbound agent processing for ${agent.name}:`, error.message);
        performanceMonitor.complete({
            status: 'error',
            error: error.message,
            stack: error.stack
        });
        throw error;
    }
}

module.exports = { outboundAgentHandler };
