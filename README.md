# Processor
## Project Structure

```
project-root/
├── config/
│   ├── database.js
│   ├── passport.js
│   ├── config.js
│   ├── logger.js
│   ├── morgan.js
│   └── rabbitmq.js
├── middlewares/
│   ├── auth.js
│   ├── error.js
│   └── rateLimiter.js
├── models/
│   ├── identity.model.js
│   ├── accessLevel.model.js
│   └── permission.model.js
├── seeders/
│   └── 20250210061754-event-config-seeder.js
├── services/
│   ├── auth.service.js
│   └── event.service.js
├── utils/
│   ├── ApiError.js
│   └── ApiResponse.js
├── .env.local
├── .env.dev
├── .gitignore
├── .sequelizerc
├── app.js
├── server.js
├── package.json
├── package-lock.json
└── README.md
```

## Naming Conventions

- **Folders:** Lowercase, use plural nouns (e.g., `controllers/`, `models/`).
- **Configuration Files:** Lowercase, camel-case (e.g., `database.js`).
- **Model Files:** Lowercase, words separated by dots, use singular nouns (e.g., `identity.model.js`).
- **Controller Files:** Lowercase, words separated by dots, use singular nouns (e.g., `identity.controller.js`).
- **Services Files:** Lowercase, words separated by dots, use singular nouns (e.g., `auth.service.js`).
- **Route Files:** Lowercase, camel-case, use singular nouns (e.g., `identityRoute.js`).
- **Middleware Files:** Lowercase, camel-case (e.g., `rateLimiter.js`).


## Description of Directories

- **config/**: Contains configuration files, such as database configurations.
- **controllers/**: Houses functions that handle incoming requests and responses.
- **models/**: Defines data schemas and interacts with the database.
- **routes/**: Defines application routes and associates them with controller functions.
- **middlewares/**: Contains middleware functions for tasks like authentication.
- **services/**: Encapsulates business logic and external service interactions.
- **utils/**: Includes utility functions and helpers used across the application.
- **tests/**: Contains unit and integration tests, organized respectively.

## Add your files

```
cd existing_repo
git add .
git commit -m "Commit message"
git remote add origin https://git.onetalkhub.com/care/processor.git
git branch -M main
git push -uf origin main
```

## Development Setup and Run

- Basic setup and installation
```
npm install
npm run dev
```
- Create enviorment file with name `.env` and put environment variables:
```
NODE_ENV
PORT
DB_URL
JWT_SECRET
```

## Command for starting the HL7 project on server using PM2 service
```pm2 start ecosystem.config.js --only hl7_parser```

## Command for starting the Patient Admission Processor on server using PM2 service
```pm2 start ecosystem.config.js --only patient_admission_procesor```

## Command for starting the Email Sending Queue on server using PM2 service
```pm2 start ecosystem.config.js --only email_procesor```

## For syncing the database
```npm run db```

## For syncing the database with particular enviorment
```npm run db -- --env-file .env.dev```

## For refreshing the database (remove all tables and then re-create)
```npm run db:refresh```

## For refreshing the database with particular enviorment (remove all tables and then re-create)
```npm run db:refresh -- --env-file .env.dev```

## For refreshing the particular models
```npm run db:refresh -- --model StagingData Facility Floor Room```

## For running the migrations
```npx sequelize-cli db:migrate```

## For undo the migrations
```npx sequelize-cli db:migrate:undo```

## For seeding all seeder 
```npx sequelize-cli db:seed:all```

## For reverting the seeded data from a particular seeder 
```npx sequelize-cli db:seed:undo:all```

## For seeding a particular seeder 
```npx sequelize-cli db:seed --seed 20230409123456-seed-users.js```

## For reverting the seeded data from a particular seeder 
```npx sequelize-cli db:seed:undo --seed 20250210061756-seed-permissions-admin-role.js```

## For seeding the data with particular enviorment (default 'local')
```npx sequelize-cli db:seed:all --env-file .env.development```

## Agent System

The agent system processes data through inbound and outbound agents. Agents are identified by unique program-friendly names (snake_case format).

### Running Agents

To run a specific agent:
```bash
# Run the automation script with a specific agent
node agent.js --agent local_connection_batch_100

# Or run the main process directly
node index.js --agent local_connection_batch_100
```

### Agent Types

- **Inbound Agents**: Process incoming data from various sources (FTP, S3, Azure, Local, URL)
- **Outbound Agents**: Generate CSV files from database models

### Agent Naming Convention

Agent names use snake_case format for program-friendly identification:
- `local_connection_batch_100` - Local directory agent with batch size 100
- `ftp_connection_main` - Main FTP server connection
- `s3_connection_production` - Production S3 bucket connection
- `azure_connection_backup` - Backup Azure storage connection

### Available Agents

Use the seeder to see all available agents or check the database `agent` table for active agents.