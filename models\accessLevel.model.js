const { Status, AccessLevelType } = require("../config/attributes");
const history = require("../utils/plugins/history_plugin");

module.exports = (sequelize, DataTypes) => {
  const AccessLevel = sequelize.define(
    "AccessLevel",
    {
      access_level_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      name: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      pacs_area_name: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      pacs_access_level_id: {
        type: DataTypes.STRING(50),
        unique: true,
        allowNull: true,
      },
      system_id: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: "system",
          key: "system_id",
        },
      },
      card_id: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: "card",
          key: "card_id",
        },
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      access_level_type: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      facility_id: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: "facility",
          key: "facility_id",
        },
      },
      online: {
        type: DataTypes.BOOLEAN,
        default: true,
      },
      requestable_self_service: {
        type: DataTypes.BOOLEAN,
        default: true,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "access_level",
      timestamps: true,
      underscored: true,
    }
  );

  AccessLevel.associate = (models) => {
    AccessLevel.hasMany(models.FacilityAccessLevel, {
      foreignKey: "access_level_id",
      as: "facility_access_level", // Must match `as` used in `include`
    });
    // Each AccessLevel belongs to a System
    AccessLevel.belongsTo(models.System, {
      foreignKey: "system_id",
      as: "system",
    });
    // Each AccessLevel belongs to a Facility
    AccessLevel.belongsTo(models.Facility, {
      foreignKey: "facility_id",
      as: "facility",
    });
    AccessLevel.belongsTo(models.Card, {
      foreignKey: "card_id",
      as: "card",
    });

    AccessLevel.belongsTo(models.MasterData, {
      foreignKey: "status",
      targetKey: "key",
      as: "status_name",
      constraints: false,
      scope: {
        group: "status",
      },
    });
    AccessLevel.belongsTo(models.MasterData, {
      foreignKey: "access_level_type",
      targetKey: "key",
      as: "access_level_type_name",
      constraints: false,
      scope: {
        group: "access_level_type",
      },
    });
  };

  history(AccessLevel, sequelize, DataTypes);

  return AccessLevel;
};
